import path, { dirname, join, resolve } from 'path';
import type { StorybookConfig } from '@storybook/nextjs';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
import { createRequire } from 'module';

type WebpackArgs = NonNullable<StorybookConfig['webpackFinal']> extends (...args: infer U) => any ? U : never;

type WebpackConfig = WebpackArgs[0];
type WebpackOptions = WebpackArgs[1];

function getAbsolutePath(value: string): any {
  const require = createRequire(import.meta.url);
  const packagePath = dirname(require.resolve(join(value, 'package.json')));
  return packagePath;
}

// SIMPLE: Just list the exact story files we want
const config: StorybookConfig = {
  stories: [
    '../src/components/legacy/CMS/content-types/VisualNavigation/stories/index-story.tsx',
    '../src/components/legacy/CMS/content-types/VisualNavigation/stories/VisualNavigationSizeToggle-story.tsx'
  ],
  core: {
    disableTelemetry: true,
  },
  addons: [
    // Only essential addon
    getAbsolutePath('@storybook/addon-links')
  ],
  framework: {
    name: getAbsolutePath('@storybook/nextjs'),
    options: {
      builder: {
        useSWC: true,
      }
    },
  },
  typescript: {
    reactDocgen: false, // Disable for faster builds
    check: false,
  },
  webpackFinal: (config: WebpackConfig, options: WebpackOptions) => {
    // Minimal webpack config for fastest builds
    config.cache = {
      type: 'filesystem',
      cacheDirectory: path.resolve(__dirname, '../node_modules/.cache/storybook'),
    };

    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        'next/headers': path.resolve(__dirname, './mock.js'),
      };
    }
    config.resolve = config.resolve || {};
    config.resolve.plugins = [
      ...(config.resolve.plugins || []),
      new TsconfigPathsPlugin({
        configFile: path.resolve(__dirname, '../tsconfig.json'),
      }),
    ];

    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@mui': path.resolve(__dirname, '../src'),
      '@ecom-next/core/legacy/link': '@ecom-next/core/migration/link',
      'uuid/v4': path.resolve(__dirname, '../../../node_modules/uuid/dist/v4.js'),
    };

    // Fast development mode
    config.devtool = 'eval-cheap-module-source-map';

    return config;
  },
  staticDirs: ['../public'],
};

export default config;
