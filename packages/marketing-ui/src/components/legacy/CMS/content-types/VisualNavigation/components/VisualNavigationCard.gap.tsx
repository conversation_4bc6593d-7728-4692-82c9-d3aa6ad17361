'use client';
import React from 'react'; // @ts-ignore
import { css, styled, Theme } from '@ecom-next/core/react-stitch';
import { VisualNavigationCategoryCard } from '../VisualNavigation/types';
import {
  VisualNavigationCard as Card,
  VisualNavigationDescriptionOverlay,
  VisualNavigationFooter,
} from '../../../components/visual-navigation/index';
import { RichText } from '../../../subcomponents/RichText/index';
import { useThemeColors } from '../../../../hooks/useThemeColors/index';
import { formatUrl } from '../../../../helper/index';
import { useViewportIsMobile } from '../../../../hooks/useViewportIsMobile/index';
import { toggleCarouselStyles, toggleCarouselStylesMobile } from './VisNavToggleCarouselStyles.gap';

/**
 * TODO: Explicitly using thergb(23, 14, 14) until it is adopted into the theme
 */
// export const OVERRIDDEN_B1_COLOR = "#2b2b2b";

const StyledCard = styled(Card)<{
  color: string;
  isToggleCarousel?: boolean;
  selected?: boolean;
}>`
  ${({ isToggleCarousel, selected, color }) => {
    const borderStyle = `2px solid ${color}`;
    const titleBorderStyle = `1px solid ${color}`;

    if (isToggleCarousel) {
      if (selected) {
        return css`
          & > div:nth-of-type(1) {
            border: ${borderStyle};
          }
        `;
      }
      return null;
    }

    return css`
      & > div:nth-of-type(2) {
        border-top: ${titleBorderStyle};
      }

      &:not(:first-of-type) > div:nth-of-type(2) {
        border-left: ${titleBorderStyle};
      }

      ${selected &&
      css`
        border-left: ${borderStyle};
        border-right: ${borderStyle};
        border-top: ${titleBorderStyle};
      `}
    `;
  }}
`;

function getHeadingStateColor(
  colors: Theme['color'],
  { hovering = false, focused = false, selected = false }: {
    focused?: boolean;
    hovering?: boolean;
    selected?: boolean
  },
): string | undefined {
  if (!hovering && (focused || selected)) {
    return colors.wh;
  }
  return undefined;
}

interface VisualNavigationCardProps {
  card: VisualNavigationCategoryCard;
  className?: string;
  headingStyle?: 'primary' | 'secondary';
  selected?: boolean;
  imageAspectRatio: string;
  showHover?: boolean;
  isImageFixedHeight?: boolean;
  isToggleCarousel?: boolean;
}

const VisualNavigationCard = (props: VisualNavigationCardProps) => {
  const {
    card,
    className,
    selected,
    imageAspectRatio,
    showHover = true,
    isImageFixedHeight = false,
    isToggleCarousel = false,
  } = props;
  const { heading, description, hoverOptions = [], image = [] } = card;
  const colors = useThemeColors();
  const [source] = image;
  const [hoverOption] = hoverOptions;
  const { image: images = [] } = hoverOption || {};
  const [hoverSource] = images;
  const scalableText = { enable: true, disableInfiniteScaling: true };
  const url = formatUrl(card.url?.value || '');
  const checkMobile = useViewportIsMobile();
  const deviceType = checkMobile ? 'mobile' : 'desktop';

  const HeadingComponent: React.FC<{ focused: boolean; isHovered: boolean }> = ({ focused, isHovered }) => {
    if (isToggleCarousel) {
      const styles = deviceType === 'mobile' ? { ...toggleCarouselStylesMobile } : { ...toggleCarouselStyles };
      return (
        <div className={css({ ...styles, paddingTop: 8 })()}>
          <span
            className={css({
              display: 'inline-block',
              borderBottom: isHovered ? `2px solid ${colors.b1}` : undefined,
            })()}
          >
            {heading}
          </span>
        </div>
      );
    }

    return (
      <RichText
        color={focused ? 'inherit' : undefined}
        css={{
          opacity: isHovered ? 0.65 : 1,
          boxSizing: 'border-box',
          padding: '11px 15px 9px',
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          '> *': {
            width: '100%',
          },
          ...(isToggleCarousel ? { ...toggleCarouselStyles } : {}),
        }}
        scalableText={scalableText}
        text={heading}
      />
    );
  };

  return (
    <StyledCard
      className={className}
      color={colors.b1}
      hoverImage={hoverSource}
      image={source}
      imageAspectRatio={imageAspectRatio}
      isImageFixedHeight={isImageFixedHeight}
      overlay={hoverOption}
      selected={selected}
      url={url}
      zoom={hoverOption?.zoom}
      isToggleCarousel={isToggleCarousel}
    >
      {({ isHovering, focused }) => {
        const isHovered = !focused && isHovering && showHover;
        return (
          <VisualNavigationFooter
            css={{
              boxSizing: 'border-box',
              position: 'relative',
              width: '100%',
              minHeight: 44,
              backgroundColor: !isToggleCarousel && focused ? colors.b1 : colors.wh,
              color: !isToggleCarousel
                ? getHeadingStateColor(colors, {
                  focused,
                  hovering: isHovered,
                  selected,
                })
                : colors.b1,
            }}
          >
            {heading && <HeadingComponent focused={!!focused} isHovered={!!isHovered} />}
            {description && (
              <VisualNavigationDescriptionOverlay
                aria-hidden={!isHovering || focused}
                bgColor="#FFFFFF"
                css={{
                  width: '100%',
                }}
                paddingBlock={15}
                paddingInline={15}
                show={isHovered}
              >
                <RichText scalableText={scalableText} text={description} />
              </VisualNavigationDescriptionOverlay>
            )}
          </VisualNavigationFooter>
        );
      }}
    </StyledCard>
  );
};

export default VisualNavigationCard;
