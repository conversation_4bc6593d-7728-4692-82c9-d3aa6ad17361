// @ts-nocheck
'use client';
import React from 'react';
import { StoryFn } from '@storybook/react';
import { checkbox, NullJSX, object, select } from '../../../../stories/story-helpers';
import VisualNavigation from '../VisualNavigation';
import { VisualNavigationProps } from '../VisualNavigation/types';
import {
  introCardData,
  MockVisualNavigationCategoryCard1,
  MockVisualNavigationCategoryCard2,
  MockVisualNavigationCategoryCard3,
  MockVisualNavigationCategoryCard4,
  MockVisualNavigationCategoryCard5,
  MockVisualNavigationCategoryCard6,
  MockVisualNavigationProdData,
  VisualNavigationDefaultData,
  VisualNavigationNewData,
} from '../VisualNavigation/__fixtures__/test-data';

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/VisualNavigation',
  parameters: {
    page: null,
    knobs: {
      disabled: true,
    },
    eyes: { include: false },
    layout: 'fullscreen',
    sandbox: true,
  },
  tags: ['visual:check'],
};

export const Playground: StoryFn<{ data: VisualNavigationProps }> = props => {
  const content = <VisualNavigation {...props.data} />;

  return (
    <NullJSX content={content}>
      <p>Unsupported by brand</p>
    </NullJSX>
  );
};
Playground.argTypes = {
  data: {
    name: 'Amplience Data',
    description: 'The JSON payload from Amplience',
    control: {
      type: 'object',
    },
  },
};

Playground.args = {
  data: VisualNavigationDefaultData,
};

export const WithHeadlineBackground = Playground.bind({});
WithHeadlineBackground.args = { data: VisualNavigationNewData };
WithHeadlineBackground.argTypes = {
  data: {
    name: 'Amplience Data',
    description: 'The JSON payload from Amplience',
    control: {
      type: 'object',
    },
  },
};

export const VisualNavigationWithPOIForON = Playground.bind({});
VisualNavigationWithPOIForON.args = { data: MockVisualNavigationProdData };
VisualNavigationWithPOIForON.argTypes = {
  data: {
    name: 'Amplience Data',
    description: 'The JSON payload from Amplience',
    control: {
      type: 'object',
    },
  },
};

export const VisualNavigationDefault: StoryFn<{
  data: VisualNavigationProps;
  introCard: boolean;
  iconPlacement: 'above' | 'below' | undefined;
  numberOfCards: number;
  headingStyle: 'primary' | 'secondary';
}> = props => {
  let visualNavData = { ...VisualNavigationDefaultData };

  const categoryCardsData = (numberOfCards: number) =>
    [
      MockVisualNavigationCategoryCard1,
      MockVisualNavigationCategoryCard2,
      MockVisualNavigationCategoryCard3,
      MockVisualNavigationCategoryCard4,
      MockVisualNavigationCategoryCard5,
      MockVisualNavigationCategoryCard6,
    ].slice(0, numberOfCards || 6);

  if (props.introCard) {
    visualNavData = { ...introCardData };
  }

  const { iconPlacement, headingStyle } = props;

  visualNavData.webAppearance = {
    imageOrIconPlacement: iconPlacement,
    showHideBasedOnScreenSize: 'alwaysShow',
    desktopImageOrIconSize: '64px',
    mobileImageOrIconSize: '48px',
    headingStyle,
  };

  const visualNavCardData = categoryCardsData(props.numberOfCards);

  visualNavData.categoryCards = [...visualNavCardData];

  const content = <VisualNavigation {...visualNavData} />;

  return (
    <div>
      <NullJSX content={content}>
        <p>Unsupported by brand</p>
      </NullJSX>
      <div className="product-grid" css={{ marginTop: '100em', width: '100%', textAlign: 'center' }}>
        Jump-to-able Section
      </div>
    </div>
  );
};
const cardCountArray = [1, 2, 3, 4, 5, 6];
VisualNavigationDefault.argTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  data: object('ISM Amplience Data'),
  introCard: checkbox('Display Intro Card'),
  iconPlacement: select('Icon Placement', ['above', 'below']),
  headingStyle: select('Heading Style', ['primary', 'secondary']),
  numberOfCards: select('Number of Category Cards', cardCountArray),
};

VisualNavigationDefault.args = {
  data: VisualNavigationDefaultData,
  introCard: false,
  iconPlacement: 'above',
  numberOfCards: 4,
  headingStyle: 'primary',
};

export const AlignmentIssueNC0688050: StoryFn<{ data: VisualNavigationProps }> = props => {
  // Console logs to debug isToggleCarousel and deviceType values
  const content = <VisualNavigation {...props.data} />;

  return (
    <NullJSX content={content}>
      <p>Unsupported by brand</p>
    </NullJSX>
  );
};

AlignmentIssueNC0688050.storyName = 'NC0688050 - Alignment Issue';
AlignmentIssueNC0688050.argTypes = {
  data: {
    name: 'Amplience Data',
    description: 'Test case for JIRA NC0688050 - Visual Navigation alignment issue with centered text and multi-line labels',
    control: {
      type: 'object',
    },
  },
};

AlignmentIssueNC0688050.args = {
  data: {
    _meta: {
      name: 'Visual Navigation - JR',
      schema: 'https://cms.gap.com/schema/content/v1/visual-navigation.json',
      deliveryId: 'a1f007e1-a0a5-4a0b-ad7a-9924f804e8ba',
    },
    categoryCards: [
      {
        image: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '79679827-2688-4502-ac3e-909623fd2512',
              name: 'SP258369_slimskinny',
              endpoint: 'gap',
              defaultHost: '1qm2jngqyb45s1j3ksoortf4y7.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        heading: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">Slim &amp; Skinny</span></p>',
        url: {
          value: '#',
        },
        description: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">This is a description. DO NOT UPDATE!</span></p>',
      },
      {
        image: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '143ed554-9967-4bd0-a14a-495704895382',
              name: 'SP258369_Baggy',
              endpoint: 'gap',
              defaultHost: '1qm2jngqyb45s1j3ksoortf4y7.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        url: {
          value: '#fit=1856',
        },
        heading: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">Slim &amp; Skinny &amp; Baggy</span></p>',
      },
      {
        image: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'a3138816-404f-43a5-aeb6-eb7ed61956ba',
              name: 'SP258369_Loose',
              endpoint: 'gap',
              defaultHost: '1qm2jngqyb45s1j3ksoortf4y7.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        url: {
          value: '#',
        },
        heading: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">This is a description. DO NOT UPDATE!</span></p>',
        description: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">This is a description. DO NOT UPDATE!</span></p>',
      },
      {
        image: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '1f75dd63-c229-41b0-8c8f-cf3bc3e4cd38',
              name: 'SP258369_straight',
              endpoint: 'gap',
              defaultHost: '1qm2jngqyb45s1j3ksoortf4y7.staging.bigcontent.io',
              mimeType: 'image/png',
            },
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        url: {
          value: '#',
        },
        heading: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">Joggers &amp; Sweats</span></p>',
      },
    ],
    webAppearance: {
      imageOrIconPlacement: 'above',
      showHideBasedOnScreenSize: 'alwaysShow',
      desktopImageOrIconSize: '24px',
      mobileImageOrIconSize: '14px',
    },
  } as VisualNavigationProps,
};
